"""
Jax router.
"""

import logging

from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from api.database import get_db
from api.models.tb_user import TbUser
from api.schemas.common_schema import Response
from api.schemas.jax_schema import JaxServiceRequest, JaxToolListResponse
from pydantic import BaseModel
from typing import List
from api.services.jax_service import JaxService
from api.utils.security_util import get_current_user

# Get logger
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/jax-dataservice", tags=["jax-dataservice"])


class JaxImportRequest(BaseModel):
    """Jax import request schema."""
    selected_tool_names: List[str]
    base_urls: List[str]
    api_key: str


@router.post("/analyze", response_model=Response[JaxToolListResponse])
async def analyze_jax_dataservice(
    request_data: JaxServiceRequest,
    db: AsyncSession = Depends(get_db),
    current_user: TbUser = Depends(get_current_user),
):
    """
    Analyze Jax platform data services and convert to tool list.

    Args:
        request_data: Jax service request data containing base URLs and API key
        db: Database session
        current_user: Current user

    Returns:
        Response[JaxToolListResponse]: List of converted tools from Jax platform
    """
    logger.info(
        f"User {current_user.username} is analyzing Jax data services from {len(request_data.base_urls)} platforms"
    )

    # Create Jax service
    service = JaxService(db)

    # Get data service tools from Jax platform
    result = await service.list_data_service_tool(
        base_urls=request_data.base_urls,
        api_key=request_data.api_key
    )

    logger.info(
        f"Successfully analyzed {len(result.tools)} tools from Jax platforms for user {current_user.username}"
    )

    return Response(data=result)


@router.post("/import", response_model=Response[List[dict]])
async def import_jax_dataservice(
    request_data: JaxImportRequest,
    db: AsyncSession = Depends(get_db),
    current_user: TbUser = Depends(get_current_user),
):
    """
    Import selected Jax data service tools.

    Args:
        request_data: Jax import request data
        db: Database session
        current_user: Current user

    Returns:
        Response[List[dict]]: List of import results
    """
    logger.info(
        f"User {current_user.username} is importing {len(request_data.selected_tool_names)} Jax tools"
    )

    # Create Jax service
    service = JaxService(db)

    # Import selected tools
    results = await service.import_data_service_tools(
        selected_tool_names=request_data.selected_tool_names,
        base_urls=request_data.base_urls,
        api_key=request_data.api_key,
        current_user=current_user.username
    )

    success_count = len([r for r in results if r.get("status") == "success"])
    logger.info(
        f"Successfully imported {success_count}/{len(request_data.selected_tool_names)} Jax tools for user {current_user.username}"
    )

    return Response(data=results)
