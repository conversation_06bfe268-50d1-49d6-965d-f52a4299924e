"""
Jax router.
"""

import logging

from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from api.database import get_db
from api.models.tb_user import TbUser
from api.schemas.common_schema import Response
from api.schemas.jax_schema import JaxServiceRequest, JaxToolListResponse
from api.services.jax_service import JaxService
from api.utils.security_util import get_current_user

# Get logger
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/jax-dataservice", tags=["jax-dataservice"])


@router.post("/analyze", response_model=Response[JaxToolListResponse])
async def analyze_jax_dataservice(
    request_data: JaxServiceRequest,
    db: AsyncSession = Depends(get_db),
    current_user: TbUser = Depends(get_current_user),
):
    """
    Analyze Jax platform data services and convert to tool list.

    Args:
        request_data: Jax service request data containing base URLs and API key
        db: Database session
        current_user: Current user

    Returns:
        Response[JaxToolListResponse]: List of converted tools from Jax platform
    """
    logger.info(
        f"User {current_user.username} is analyzing Jax data services from {len(request_data.base_urls)} platforms"
    )

    # Create Jax service
    service = JaxService(db)
    
    # Get data service tools from Jax platform
    result = await service.list_data_service_tool(
        base_urls=request_data.base_urls,
        api_key=request_data.api_key
    )

    logger.info(
        f"Successfully analyzed {len(result.tools)} tools from Jax platforms for user {current_user.username}"
    )

    return Response(data=result)
