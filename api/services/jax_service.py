"""
Jax service for integrating with Jax platform data services.

This module provides functionality to:
1. Fetch data service list from Jax platform
2. Convert Jax data services to tool format
3. Handle authentication with app<PERSON>ey and secret<PERSON>ey
"""

import logging
from typing import List, Dict, Any

import httpx
from sqlalchemy.ext.asyncio import AsyncSession

from api.errors.base_error import ServiceError
from api.schemas.jax_schema import (
    JaxDataServiceResponse,
    JaxToolInfo,
    JaxToolListResponse,
)

# Get logger
logger = logging.getLogger(__name__)


class JaxService:
    """
    Service for integrating with Jax platform data services.

    This service provides methods to:
    1. list_data_service_tool: Fetch Jax platform data services and convert to tools

    It handles:
    - HTTP requests to Jax platform API
    - Authentication with app<PERSON>ey and secretKey
    - Data transformation from Jax format to tool format
    - Error handling and logging
    """

    def __init__(self, db: AsyncSession):
        """
        Initialize Jax service.

        Args:
            db: Database session
        """
        self.db = db

    async def list_data_service_tool(
        self, base_urls: List[str], api_key: str
    ) -> JaxToolListResponse:
        """
        Get Jax platform data service list and convert to tool list.

        This method fetches data services from multiple Jax platform instances,
        filters for HTTP consume mode services, and converts them to tool format.

        Args:
            base_urls: List of Jax platform base URLs
            api_key: API key for authentication (used as secretKey)

        Returns:
            JaxToolListResponse: List of converted tools

        Raises:
            ServiceError: If API request fails or data processing fails
        """
        all_tools = []

        for base_url in base_urls:
            try:
                # Construct API URL
                api_url = f"{base_url.rstrip('/')}/api/v2/data-service/api/list"

                # Prepare headers for authentication
                headers = {
                    "appKey": "jax",
                    "secretKey": api_key,
                    "Content-Type": "application/json",
                }

                logger.info(f"Fetching data services from Jax platform: {api_url}")

                # Make HTTP request
                async with httpx.AsyncClient(timeout=30.0) as client:
                    response = await client.get(api_url, headers=headers)
                    response.raise_for_status()

                    # Parse response
                    response_data = response.json()

                    # Validate response structure
                    jax_response = JaxDataServiceResponse(**response_data)

                    # Check if request was successful
                    if jax_response.code != "0000":
                        logger.warning(
                            f"Jax API returned error code {jax_response.code}: {jax_response.msg}"
                        )
                        continue

                    # Convert data services to tools
                    tools = self._convert_services_to_tools(jax_response.data)
                    all_tools.extend(tools)

                    logger.info(
                        f"Successfully fetched {len(tools)} tools from {base_url}"
                    )

            except httpx.HTTPStatusError as e:
                logger.error(
                    f"HTTP error when fetching from {base_url}: {e.response.status_code} - {e.response.text}"
                )
                raise ServiceError(
                    reason=f"Jax平台API请求失败: HTTP {e.response.status_code}",
                    description=f"请求URL: {base_url}, 错误: {e.response.text}",
                    code="JAX_API_HTTP_ERROR",
                )
            except httpx.RequestError as e:
                logger.error(f"Request error when fetching from {base_url}: {str(e)}")
                raise ServiceError(
                    reason=f"Jax平台连接失败: {str(e)}",
                    description=f"请求URL: {base_url}",
                    code="JAX_API_CONNECTION_ERROR",
                )
            except Exception as e:
                logger.error(f"Unexpected error when fetching from {base_url}: {str(e)}")
                raise ServiceError(
                    reason=f"Jax平台数据处理失败: {str(e)}",
                    description=f"请求URL: {base_url}",
                    code="JAX_API_PROCESSING_ERROR",
                )

        # Sort tools by name
        all_tools.sort(key=lambda tool: tool.name)

        logger.info(f"Total {len(all_tools)} tools fetched from all Jax platforms")

        return JaxToolListResponse(tools=all_tools)

    def _convert_services_to_tools(self, services: List[Any]) -> List[JaxToolInfo]:
        """
        Convert Jax data services to tool format.

        Args:
            services: List of Jax data services

        Returns:
            List[JaxToolInfo]: List of converted tools
        """
        tools = []

        for service in services:
            try:
                # Filter only HTTP consume mode services
                if service.consumeMode != "HTTP":
                    continue

                # Generate tool name with jax_ prefix
                tool_name = f"jax_{service.apiPath}"

                # Generate tool description
                description = service.name
                if service.description and service.description != service.name:
                    description += f" - {service.description}"

                # Convert requestColumns to JSON Schema parameters
                parameters = self._convert_request_columns_to_schema(
                    service.setting.requestColumns or []
                )

                # Create tool info
                tool = JaxToolInfo(
                    name=tool_name,
                    description=description,
                    parameters=parameters,
                    api_path=service.apiPath,
                    request_type=service.requestType or "POST",  # Default to POST if not specified
                )

                tools.append(tool)

            except Exception as e:
                logger.warning(
                    f"Failed to convert service {getattr(service, 'apiPath', 'unknown')}: {str(e)}"
                )
                continue

        return tools

    def _convert_request_columns_to_schema(
        self, request_columns: List[Any]
    ) -> Dict[str, Any]:
        """
        Convert Jax requestColumns to JSON Schema format.

        Args:
            request_columns: List of request columns (can be empty)

        Returns:
            Dict[str, Any]: JSON Schema format parameters
        """
        properties = {}
        required = []

        # Handle empty request columns
        if not request_columns:
            return {
                "type": "object",
                "properties": {},
                "required": [],
            }

        for column in request_columns:
            # Map Jax types to JSON Schema types
            json_type = self._map_jax_type_to_json_type(column.type)

            # Create property definition
            property_def = {
                "type": json_type,
                "description": column.desc or column.name,
            }

            properties[column.name] = property_def

            # For now, assume all parameters are required
            # This can be enhanced based on Jax API specification
            required.append(column.name)

        return {
            "type": "object",
            "properties": properties,
            "required": required,
        }

    def _map_jax_type_to_json_type(self, jax_type: str) -> str:
        """
        Map Jax data type to JSON Schema type.

        Args:
            jax_type: Jax data type

        Returns:
            str: JSON Schema type
        """
        type_mapping = {
            "STRING": "string",
            "INTEGER": "integer",
            "INT": "integer",
            "LONG": "integer",
            "DOUBLE": "number",
            "FLOAT": "number",
            "BOOLEAN": "boolean",
            "LIST": "array",
        }

        return type_mapping.get(jax_type.upper(), "string")