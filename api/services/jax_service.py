"""
Jax service for integrating with Jax platform data services.

This module provides functionality to:
1. Fetch data service list from Jax platform
2. Convert Jax data services to tool format
3. Handle authentication with app<PERSON><PERSON> and secret<PERSON>ey
"""

import json
import logging
from typing import List, Dict, Any, Optional

import httpx
from sqlalchemy.ext.asyncio import AsyncSession

from api.errors.base_error import ServiceError
from api.schemas.jax_schema import (
    JaxDataServiceResponse,
    JaxToolInfo,
    JaxToolListResponse,
)
from api.schemas.tool_schema import ToolCreate
from api.schemas.func_schema import FuncCreate
from api.schemas.config_schema import ConfigCreate
from api.utils.time_util import get_current_unix_ms

# Get logger
logger = logging.getLogger(__name__)


class JaxService:
    """
    Service for integrating with Jax platform data services.

    This service provides methods to:
    1. list_data_service_tool: Fetch Jax platform data services and convert to tools

    It handles:
    - HTTP requests to Jax platform API
    - Authentication with app<PERSON><PERSON> and secretKey
    - Data transformation from Jax format to tool format
    - Error handling and logging
    """

    def __init__(self, db: AsyncSession):
        """
        Initialize Jax service.

        Args:
            db: Database session
        """
        self.db = db

    async def list_data_service_tool(
        self, base_urls: List[str], api_key: str
    ) -> JaxToolListResponse:
        """
        Get Jax platform data service list and convert to tool list.

        This method fetches data services from multiple Jax platform instances,
        filters for HTTP consume mode services, and converts them to tool format.

        Args:
            base_urls: List of Jax platform base URLs
            api_key: API key for authentication (used as secretKey)

        Returns:
            JaxToolListResponse: List of converted tools

        Raises:
            ServiceError: If API request fails or data processing fails
        """
        all_tools = []

        for base_url in base_urls:
            try:
                # Construct API URL
                api_url = f"{base_url.rstrip('/')}/api/v2/data-service/api/list"

                # Prepare headers for authentication
                headers = {
                    "appKey": "jax",
                    "secretKey": api_key,
                    "Content-Type": "application/json",
                }

                logger.info(f"Fetching data services from Jax platform: {api_url}")

                # Make HTTP request
                async with httpx.AsyncClient(timeout=30.0) as client:
                    response = await client.get(api_url, headers=headers)
                    response.raise_for_status()

                    # Parse response
                    response_data = response.json()

                    # Validate response structure
                    jax_response = JaxDataServiceResponse(**response_data)

                    # Check if request was successful
                    if jax_response.code != "0000":
                        logger.warning(
                            f"Jax API returned error code {jax_response.code}: {jax_response.msg}"
                        )
                        continue

                    # Convert data services to tools
                    tools = self._convert_services_to_tools(jax_response.data)
                    all_tools.extend(tools)

                    logger.info(
                        f"Successfully fetched {len(tools)} tools from {base_url}"
                    )

            except httpx.HTTPStatusError as e:
                logger.error(
                    f"HTTP error when fetching from {base_url}: {e.response.status_code} - {e.response.text}"
                )
                raise ServiceError(
                    reason=f"Jax平台API请求失败: HTTP {e.response.status_code}",
                    description=f"请求URL: {base_url}, 错误: {e.response.text}",
                    code="JAX_API_HTTP_ERROR",
                )
            except httpx.RequestError as e:
                logger.error(f"Request error when fetching from {base_url}: {str(e)}")
                raise ServiceError(
                    reason=f"Jax平台连接失败: {str(e)}",
                    description=f"请求URL: {base_url}",
                    code="JAX_API_CONNECTION_ERROR",
                )
            except Exception as e:
                logger.error(f"Unexpected error when fetching from {base_url}: {str(e)}")
                raise ServiceError(
                    reason=f"Jax平台数据处理失败: {str(e)}",
                    description=f"请求URL: {base_url}",
                    code="JAX_API_PROCESSING_ERROR",
                )

        # Sort tools by name
        all_tools.sort(key=lambda tool: tool.name)

        logger.info(f"Total {len(all_tools)} tools fetched from all Jax platforms")

        return JaxToolListResponse(tools=all_tools)

    async def import_data_service_tools(
        self,
        selected_tool_names: List[str],
        base_urls: List[str],
        api_key: str,
        current_user: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Import selected Jax data service tools.

        This method creates TbTool, TbFunc, TbConfig and establishes associations
        for the selected Jax data service tools.

        Args:
            selected_tool_names: List of selected tool names to import
            base_urls: List of Jax platform base URLs
            api_key: API key for authentication
            current_user: Current username

        Returns:
            List[Dict[str, Any]]: List of imported tool information

        Raises:
            ServiceError: If import fails
        """
        # First get all available tools
        tools_response = await self.list_data_service_tool(base_urls, api_key)
        all_tools = {tool.name: tool for tool in tools_response.tools}

        # Filter selected tools
        selected_tools = []
        for tool_name in selected_tool_names:
            if tool_name in all_tools:
                selected_tools.append(all_tools[tool_name])
            else:
                logger.warning(f"Tool {tool_name} not found in available tools")

        if not selected_tools:
            raise ServiceError(
                reason="没有找到要导入的工具",
                description="所选择的工具在Jax平台中不存在",
                code="JAX_NO_TOOLS_TO_IMPORT",
            )

        # Import services
        from api.services.tool_service import ToolService
        from api.services.func_service import FuncService
        from api.services.config_service import ConfigService

        tool_service = ToolService(self.db)
        func_service = FuncService(self.db)
        config_service = ConfigService(self.db)

        imported_results = []

        # Create or get jax_service_config
        jax_config = await self._ensure_jax_service_config(config_service, current_user)

        # Create or get call_jax_service function
        jax_func = await self._ensure_call_jax_service_func(func_service, current_user)

        for tool_info in selected_tools:
            try:
                # Generate tool code
                tool_code = self._generate_jax_tool_code(tool_info)

                # Create tool
                tool_data = ToolCreate(
                    name=tool_info.name,
                    description=tool_info.description,
                    parameters=tool_info.parameters,
                    code=tool_code,
                    func_ids=[jax_func.id],
                    config_ids=[jax_config.id],
                )

                tool = await tool_service.create_tool(tool_data, current_user)

                # Deploy tool
                await tool_service.deploy_tool(
                    tool.id, f"Initial import from Jax platform: {tool_info.api_path}", current_user
                )

                # Refresh tool
                await self.db.refresh(tool)

                imported_results.append({
                    "tool_id": tool.id,
                    "tool_name": tool.name,
                    "api_path": tool_info.api_path,
                    "request_type": tool_info.request_type,
                    "status": "success"
                })

                logger.info(f"Successfully imported Jax tool: {tool.name}")

            except Exception as e:
                logger.error(f"Error importing tool {tool_info.name}: {str(e)}")
                imported_results.append({
                    "tool_name": tool_info.name,
                    "api_path": tool_info.api_path,
                    "status": "failed",
                    "error": str(e)
                })
                continue

        return imported_results

    def _convert_services_to_tools(self, services: List[Any]) -> List[JaxToolInfo]:
        """
        Convert Jax data services to tool format.

        Args:
            services: List of Jax data services

        Returns:
            List[JaxToolInfo]: List of converted tools
        """
        tools = []

        for service in services:
            try:
                # Filter only HTTP consume mode services
                if service.consumeMode != "HTTP":
                    continue

                # Generate tool name with jax_ prefix
                tool_name = f"jax_{service.apiPath}"

                # Generate tool description
                description = service.name
                if service.description and service.description != service.name:
                    description += f" - {service.description}"

                # Convert requestColumns to JSON Schema parameters
                parameters = self._convert_request_columns_to_schema(
                    service.setting.requestColumns or []
                )

                # Create tool info
                tool = JaxToolInfo(
                    name=tool_name,
                    description=description,
                    parameters=parameters,
                    api_path=service.apiPath,
                    request_type=service.requestType or "POST",  # Default to POST if not specified
                )

                tools.append(tool)

            except Exception as e:
                logger.warning(
                    f"Failed to convert service {getattr(service, 'apiPath', 'unknown')}: {str(e)}"
                )
                continue

        return tools

    def _convert_request_columns_to_schema(
        self, request_columns: List[Any]
    ) -> Dict[str, Any]:
        """
        Convert Jax requestColumns to JSON Schema format.

        Args:
            request_columns: List of request columns (can be empty)

        Returns:
            Dict[str, Any]: JSON Schema format parameters
        """
        properties = {}
        required = []

        # Handle empty request columns
        if not request_columns:
            return {
                "type": "object",
                "properties": {},
                "required": [],
            }

        for column in request_columns:
            # Map Jax types to JSON Schema types
            json_type = self._map_jax_type_to_json_type(column.type)

            # Create property definition
            property_def = {
                "type": json_type,
                "description": column.desc or column.name,
            }

            properties[column.name] = property_def

            # For now, assume all parameters are required
            # This can be enhanced based on Jax API specification
            required.append(column.name)

        return {
            "type": "object",
            "properties": properties,
            "required": required,
        }

    def _map_jax_type_to_json_type(self, jax_type: str) -> str:
        """
        Map Jax data type to JSON Schema type.

        Args:
            jax_type: Jax data type

        Returns:
            str: JSON Schema type
        """
        type_mapping = {
            "STRING": "string",
            "INTEGER": "integer",
            "INT": "integer",
            "LONG": "integer",
            "DOUBLE": "number",
            "FLOAT": "number",
            "BOOLEAN": "boolean",
            "LIST": "array",
        }

        return type_mapping.get(jax_type.upper(), "string")

    async def _ensure_jax_service_config(self, config_service, current_user: Optional[str] = None):
        """
        Ensure jax_service_config exists, create if not.

        Args:
            config_service: ConfigService instance
            current_user: Current username

        Returns:
            TbConfig: Jax service configuration
        """
        # Try to get existing config
        try:
            config = await config_service.get_config_by_name("jax_service_config")
            if config:
                return config
        except:
            pass

        # Create new config
        config_schema = {
            "type": "object",
            "properties": {
                "api_key": {
                    "type": "string",
                    "description": "Jax服务授权ApiKey"
                },
                "secret_key": {
                    "type": "string",
                    "description": "Jax服务授权SecretKey"
                },
                "jax_server": {
                    "type": "string",
                    "description": "Jax服务器地址"
                }
            },
            "required": []
        }

        conf_value = {
            "api_key": "",
            "secret_key": "",
            "jax_server": ""
        }

        config_data = ConfigCreate(
            name="jax_service_config",
            description="Jax数据服务配置",
            conf_schema=config_schema,
            conf_value=conf_value,
        )

        return await config_service.create_config(config_data, current_user)

    async def _ensure_call_jax_service_func(self, func_service, current_user: Optional[str] = None):
        """
        Ensure call_jax_service function exists, create if not.

        Args:
            func_service: FuncService instance
            current_user: Current username

        Returns:
            TbFunc: call_jax_service function
        """
        # Try to get existing function
        try:
            func = await func_service.get_func_by_name("call_jax_service")
            if func:
                return func
        except:
            pass

        # Create new function
        func_code = '''"""
Jax service function for calling Jax platform data services.
"""

import json
import httpx


async def call_jax_service(method, url, body=None, api_key=None, secret_key=None):
    """
    Call Jax platform data service.

    Args:
        method: HTTP method (GET, POST, etc.)
        url: Full URL to call
        body: Request body (optional)
        api_key: API key for authentication
        secret_key: Secret key for authentication

    Returns:
        dict: Response data
    """
    headers = {
        "Content-Type": "application/json",
    }

    # Add authentication headers if provided
    if api_key:
        headers["appKey"] = api_key
    if secret_key:
        headers["secretKey"] = secret_key

    async with httpx.AsyncClient(timeout=30.0) as client:
        if method.upper() == "GET":
            response = await client.get(url, headers=headers)
        elif method.upper() == "POST":
            response = await client.post(url, headers=headers, json=body)
        elif method.upper() == "PUT":
            response = await client.put(url, headers=headers, json=body)
        elif method.upper() == "DELETE":
            response = await client.delete(url, headers=headers)
        else:
            raise ValueError(f"Unsupported HTTP method: {method}")

        response.raise_for_status()
        return response.json()
'''

        func_data = FuncCreate(
            name="call_jax_service",
            description="调用Jax平台数据服务的通用函数",
            code=func_code,
            depend_ids=[],
        )

        return await func_service.create_func(func_data, current_user)

    def _generate_jax_tool_code(self, tool_info: JaxToolInfo) -> str:
        """
        Generate executable script code for Jax data service.

        Args:
            tool_info: Jax tool information

        Returns:
            str: Generated executable script code
        """
        tool_code = f'''"""
{tool_info.description}

This script calls Jax platform data service: {tool_info.api_path}
"""

# Get configuration (required)
api_key = config.get("api_key")
if not api_key:
    raise ValueError("api_key is required in configuration")

jax_server = config.get("jax_server")
if not jax_server:
    raise ValueError("jax_server is required in configuration")

secret_key = config.get("secret_key", "")

# Construct URL
url = f"{{jax_server.rstrip('/')}}/api/open/data-service/{tool_info.api_path}"

# Construct request body with standard Jax format
body = {{
    "pageNum": 0,
    "pageSize": 100,
    "param": parameters
}}

# Call Jax service
result = await call_jax_service(
    method="{tool_info.request_type}",
    url=url,
    body=body,
    api_key=api_key,
    secret_key=secret_key
)
'''

        return tool_code