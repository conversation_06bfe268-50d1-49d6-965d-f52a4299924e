"""
Jax schemas.
"""

from typing import List, Optional, Dict, Any

from pydantic import BaseModel, Field


class JaxRequestColumn(BaseModel):
    """
    Jax request column schema.

    Attributes:
        name: Column name
        type: Column type
        desc: Column description
    """

    name: str = Field(description="Column name")
    type: str = Field(description="Column type")
    desc: Optional[str] = Field(default=None, description="Column description")


class JaxDataServiceSetting(BaseModel):
    """
    Jax data service setting schema.

    Attributes:
        requestColumns: Request columns (optional)
    """

    requestColumns: Optional[List[JaxRequestColumn]] = Field(default=None, description="Request columns (optional)")


class JaxDataService(BaseModel):
    """
    Jax data service schema.

    Attributes:
        consumeMode: Consume mode
        apiPath: API path
        name: Service name
        description: Service description
        requestType: Request type (optional, only present when consumeMode is HTTP)
        setting: Service setting
    """

    consumeMode: str = Field(description="Consume mode")
    apiPath: str = Field(description="API path")
    name: str = Field(description="Service name")
    description: Optional[str] = Field(default=None, description="Service description")
    requestType: Optional[str] = Field(default=None, description="Request type (optional, only present when consumeMode is HTTP)")
    setting: JaxDataServiceSetting = Field(description="Service setting")


class JaxDataServiceResponse(BaseModel):
    """
    Jax data service response schema.

    Attributes:
        code: Response code
        msg: Response message
        data: Data services
    """

    code: str = Field(description="Response code")
    msg: str = Field(description="Response message")
    data: List[JaxDataService] = Field(description="Data services")


class JaxToolInfo(BaseModel):
    """
    Jax tool information schema.

    Attributes:
        name: Tool name
        description: Tool description
        parameters: Tool parameters (JSON Schema)
        api_path: Original API path
        request_type: Request type
    """

    name: str = Field(description="Tool name")
    description: str = Field(description="Tool description")
    parameters: Dict[str, Any] = Field(description="Tool parameters (JSON Schema)")
    api_path: str = Field(description="Original API path")
    request_type: str = Field(description="Request type")


class JaxToolListResponse(BaseModel):
    """
    Jax tool list response schema.

    Attributes:
        tools: List of Jax tools
    """

    tools: List[JaxToolInfo] = Field(description="List of Jax tools")


class JaxServiceRequest(BaseModel):
    """
    Jax service request schema.

    Attributes:
        base_urls: List of Jax platform base URLs
        api_key: API key for authentication
    """

    base_urls: List[str] = Field(description="List of Jax platform base URLs")
    api_key: str = Field(description="API key for authentication")