<template>
  <app-layout current-page-key="tool">
    <a-card title="导入 Jax 数据服务">
      <template #extra>
        <a-button @click="router.push('/tool')">
          <template #icon><RollbackOutlined /></template>
          返回
        </a-button>
      </template>

      <a-steps :current="currentStep" size="small" style="margin-bottom: 24px">
        <a-step title="配置连接" />
        <a-step title="选择数据服务" />
        <a-step title="生成工具" />
      </a-steps>

      <!-- 步骤 1: 配置连接 -->
      <div v-if="currentStep === 0" class="step-content">
        <a-alert
          message="配置 Jax 平台连接"
          description="请配置 Jax 平台的连接信息，包括中台地址和 API 密钥。系统将从这些平台获取数据服务列表。"
          type="info"
          show-icon
          style="margin-bottom: 24px"
        />

        <a-form :model="connectionForm" layout="vertical">
          <a-form-item label="中台地址列表" required>
            <div class="url-list">
              <div v-for="(url, index) in connectionForm.baseUrls" :key="index" class="url-item">
                <a-input
                  v-model:value="connectionForm.baseUrls[index]"
                  placeholder="请输入 Jax 平台地址，例如: http://***************:9900"
                  style="flex: 1"
                />
                <a-button
                  v-if="connectionForm.baseUrls.length > 1"
                  type="text"
                  danger
                  @click="removeUrl(index)"
                >
                  <template #icon><DeleteOutlined /></template>
                </a-button>
              </div>
              <a-button type="dashed" block @click="addUrl">
                <template #icon><PlusOutlined /></template>
                添加地址
              </a-button>
            </div>
          </a-form-item>

          <a-form-item label="API 密钥" required>
            <a-input-password
              v-model:value="connectionForm.apiKey"
              placeholder="请输入 Jax 平台的 secretKey"
            />
          </a-form-item>
        </a-form>

        <div class="step-actions">
          <a-button type="primary" :loading="loadingServices" @click="fetchDataServices">
            下一步
          </a-button>
        </div>
      </div>

      <!-- 步骤 2: 选择数据服务 -->
      <div v-if="currentStep === 1" class="step-content">
        <a-alert
          message="选择要导入的数据服务"
          description="请选择要导入的 Jax 数据服务，系统将根据选择的服务生成工具。"
          type="info"
          show-icon
          style="margin-bottom: 24px"
        />

        <a-spin :spinning="loadingServices">
          <div v-if="dataServices.length === 0 && !loadingServices" class="empty-state">
            <a-empty description="没有可用的数据服务" />
          </div>
          <a-table
            v-else
            :columns="serviceColumns"
            :data-source="dataServices"
            :pagination="false"
            :row-selection="{ selectedRowKeys: selectedServiceKeys, onChange: onSelectServiceChange, type: 'checkbox' }"
            :row-key="record => record.name"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'request_type'">
                <a-tag :color="getRequestTypeColor(record.request_type)">{{ record.request_type }}</a-tag>
              </template>
              <template v-if="column.key === 'parameters'">
                <a-tag v-if="record.parameters && record.parameters.properties && Object.keys(record.parameters.properties).length > 0">
                  {{ Object.keys(record.parameters.properties).length }} 个参数
                </a-tag>
                <a-tag v-else color="default">无参数</a-tag>
              </template>
            </template>
          </a-table>
        </a-spin>

        <div class="step-actions">
          <a-button @click="currentStep = 0">上一步</a-button>
          <a-button type="primary" :disabled="selectedServiceKeys.length === 0" @click="currentStep = 2">
            下一步
          </a-button>
        </div>
      </div>

      <!-- 步骤 3: 生成工具 -->
      <div v-if="currentStep === 2" class="step-content">
        <a-alert
          message="生成工具"
          description="系统将根据选择的数据服务生成工具，请确认以下信息。"
          type="info"
          show-icon
          style="margin-bottom: 24px"
        />

        <div class="selected-services">
          <h3>已选择的数据服务 ({{ selectedServiceKeys.length }})</h3>
          <a-list size="small" bordered>
            <a-list-item v-for="serviceKey in selectedServiceKeys" :key="serviceKey">
              <div class="service-item">
                <div class="service-name">{{ getServiceByKey(serviceKey).name }}</div>
                <div class="service-description">{{ getServiceByKey(serviceKey).description }}</div>
              </div>
              <template #actions>
                <a-tag>{{ getServiceByKey(serviceKey).request_type }}</a-tag>
                <a-tag color="blue">{{ getServiceByKey(serviceKey).name }}</a-tag>
              </template>
            </a-list-item>
          </a-list>
        </div>

        <div class="step-actions">
          <a-button @click="currentStep = 1">上一步</a-button>
          <a-button type="primary" :loading="generating" @click="generateTools">生成工具</a-button>
        </div>
      </div>
    </a-card>
  </app-layout>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import {
  RollbackOutlined,
  PlusOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { callApi } from '../../utils/api-util'
import AppLayout from '../../components/AppLayout.vue'

const router = useRouter()
const currentStep = ref(0)
const loadingServices = ref(false)
const generating = ref(false)

// 连接配置表单
const connectionForm = ref({
  baseUrls: [''],
  apiKey: ''
})

// 数据服务列表
const dataServices = ref([])
const selectedServiceKeys = ref([])

// 服务表格列定义
const serviceColumns = [
  {
    title: '名称',
    dataIndex: 'name',
    key: 'tool_name',
    width: 200
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description'
  },
  {
    title: '请求类型',
    dataIndex: 'request_type',
    key: 'request_type',
    width: 100
  },
  {
    title: '参数',
    key: 'parameters',
    width: 120
  }
]

// 添加 URL
const addUrl = () => {
  connectionForm.value.baseUrls.push('')
}

// 删除 URL
const removeUrl = (index) => {
  connectionForm.value.baseUrls.splice(index, 1)
}

// 获取请求类型颜色
const getRequestTypeColor = (type) => {
  const colors = {
    'GET': 'green',
    'POST': 'blue',
    'PUT': 'orange',
    'DELETE': 'red'
  }
  return colors[type] || 'default'
}

// 获取数据服务
const fetchDataServices = async () => {
  // 验证表单
  const validUrls = connectionForm.value.baseUrls.filter(url => url.trim())
  if (validUrls.length === 0) {
    message.warning('请至少输入一个中台地址')
    return
  }

  if (!connectionForm.value.apiKey.trim()) {
    message.warning('请输入 API 密钥')
    return
  }

  loadingServices.value = true

  try {
    await callApi({
      method: 'post',
      url: '/api/v1/jax-dataservice/analyze',
      data: {
        base_urls: validUrls,
        api_key: connectionForm.value.apiKey
      },
      onSuccess: (data) => {
        dataServices.value = data.tools || []
        currentStep.value = 1
      },
      errorMessage: '获取 Jax 数据服务失败'
    })
  } catch (error) {
    console.error('Failed to fetch data services:', error)
  } finally {
    loadingServices.value = false
  }
}

// 选择服务变更
const onSelectServiceChange = (keys) => {
  selectedServiceKeys.value = keys
}

// 根据 key 获取服务
const getServiceByKey = (key) => {
  return dataServices.value.find(service => service.name === key) || {}
}

// 生成工具
const generateTools = async () => {
  if (selectedServiceKeys.value.length === 0) {
    message.warning('请选择要导入的数据服务')
    return
  }

  generating.value = true

  try {
    await callApi({
      method: 'post',
      url: '/api/v1/jax-dataservice/import',
      data: {
        selected_tool_names: selectedServiceKeys.value,
        base_urls: connectionForm.value.baseUrls.filter(url => url.trim()),
        api_key: connectionForm.value.apiKey
      },
      onSuccess: (data) => {
        const successCount = data.filter(result => result.status === 'success').length
        const failedCount = data.length - successCount

        if (successCount > 0) {
          message.success(`成功导入 ${successCount} 个工具`)
        }
        if (failedCount > 0) {
          message.warning(`${failedCount} 个工具导入失败`)
        }

        router.push('/tool')
      },
      errorMessage: '导入 Jax 数据服务工具失败'
    })
  } catch (error) {
    console.error('Failed to import tools:', error)
  } finally {
    generating.value = false
  }
}
</script>

<style scoped>
.step-content {
  margin-bottom: 24px;
}

.step-actions {
  margin-top: 24px;
  display: flex;
  justify-content: space-between;
}

.url-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.url-item {
  display: flex;
  gap: 8px;
  align-items: center;
}

.selected-services {
  margin-bottom: 24px;
}

.service-item {
  flex: 1;
}

.service-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.service-description {
  color: #666;
  font-size: 12px;
}

.empty-state {
  padding: 40px;
  text-align: center;
}
</style>
